/**
 * BasicDataStore API增强版 - 修复版本
 * 🎯 目标：为混合存储提供API同步功能
 * 🔧 修复：添加缺失的 syncToApi、loadFromApi、setColorCoordinatesWithSync 方法
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import {
  useBasicDataStore,
  type BasicColorType,
  type ColorVisibility,
  type BasicDataStore
} from './basicDataStore';
import type { ColorCoordinates } from '../types/color';

// API同步状态接口
interface ApiSyncState {
  isOnline: boolean;
  isSyncing: boolean;
  lastSyncTime: Date | null;
  syncErrors: string[];
}

// API增强的BasicDataStore接口
interface ApiEnhancedBasicDataStore extends BasicDataStore {
  // API同步状态
  apiSyncState: ApiSyncState;

  // API同步方法
  syncToApi: () => Promise<void>;
  loadFromApi: () => Promise<void>;
  setColorCoordinatesWithSync: (colorType: BasicColorType, coordinates: ColorCoordinates) => Promise<void>;

  // API状态管理
  setApiSyncState: (state: Partial<ApiSyncState>) => void;
  clearSyncErrors: () => void;
}

// 默认API同步状态
const DEFAULT_API_SYNC_STATE: ApiSyncState = {
  isOnline: false,
  isSyncing: false,
  lastSyncTime: null,
  syncErrors: []
};

// 创建API增强的BasicDataStore
export const useApiEnhancedBasicDataStore = create<ApiEnhancedBasicDataStore>()(
  persist(
    (set, get) => {
      // 获取原始BasicDataStore的实例
      const basicStore = useBasicDataStore.getState();

      return {
        // 继承所有BasicDataStore的状态和方法
        ...basicStore,

        // API同步状态
        apiSyncState: DEFAULT_API_SYNC_STATE,

        // API同步方法实现
        syncToApi: async () => {
          const state = get();

          try {
            set((prev) => ({
              ...prev,
              apiSyncState: {
                ...prev.apiSyncState,
                isSyncing: true,
                syncErrors: []
              }
            }));

            // TODO: 实际的API同步逻辑
            // 这里应该调用API将当前数据同步到服务器
            console.log('🔄 模拟API同步...', {
              colorCoordinates: state.colorCoordinates,
              colorValues: state.colorValues,
              colorVisibility: state.colorVisibility
            });

            // 模拟API调用延迟
            await new Promise(resolve => setTimeout(resolve, 1000));

            set((prev) => ({
              ...prev,
              apiSyncState: {
                ...prev.apiSyncState,
                isSyncing: false,
                lastSyncTime: new Date()
              }
            }));

            console.log('✅ API同步完成');

          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '同步失败';

            set((prev) => ({
              ...prev,
              apiSyncState: {
                ...prev.apiSyncState,
                isSyncing: false,
                syncErrors: [...prev.apiSyncState.syncErrors, errorMessage]
              }
            }));

            console.error('❌ API同步失败:', error);
            throw error;
          }
        },

        loadFromApi: async () => {
          try {
            set((prev) => ({
              ...prev,
              apiSyncState: {
                ...prev.apiSyncState,
                isSyncing: true,
                syncErrors: []
              }
            }));

            // TODO: 实际的API加载逻辑
            // 这里应该从API加载数据并更新store
            console.log('📥 模拟从API加载数据...');

            // 模拟API调用延迟
            await new Promise(resolve => setTimeout(resolve, 1000));

            set((prev) => ({
              ...prev,
              apiSyncState: {
                ...prev.apiSyncState,
                isSyncing: false,
                lastSyncTime: new Date()
              }
            }));

            console.log('✅ 从API加载数据完成');

          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '加载失败';

            set((prev) => ({
              ...prev,
              apiSyncState: {
                ...prev.apiSyncState,
                isSyncing: false,
                syncErrors: [...prev.apiSyncState.syncErrors, errorMessage]
              }
            }));

            console.error('❌ 从API加载数据失败:', error);
            throw error;
          }
        },

        setColorCoordinatesWithSync: async (colorType: BasicColorType, coordinates: ColorCoordinates) => {
          // 先更新本地数据
          set((state) => ({
            ...state,
            colorCoordinates: {
              ...state.colorCoordinates,
              [colorType]: coordinates,
            },
          }));

          // 然后同步到API
          try {
            await get().syncToApi();
          } catch (error) {
            console.warn('⚠️ 本地更新成功，但API同步失败:', error);
            // 不抛出错误，因为本地更新已经成功
          }
        },

        // API状态管理
        setApiSyncState: (newState: Partial<ApiSyncState>) => {
          set((prev) => ({
            ...prev,
            apiSyncState: {
              ...prev.apiSyncState,
              ...newState
            }
          }));
        },

        clearSyncErrors: () => {
          set((prev) => ({
            ...prev,
            apiSyncState: {
              ...prev.apiSyncState,
              syncErrors: []
            }
          }));
        }
      };
    },
    {
      name: 'api-enhanced-basic-data-store',
      version: 1,
      // 不持久化API同步状态
      partialize: (state) => {
        const { apiSyncState, ...persistedState } = state;
        return persistedState;
      }
    }
  )
);

// 重新导出原始的选择器和类型
export {
  useColorCoordinates as useApiColorCoordinates,
  useColorValues as useApiColorValues,
  type BasicColorType,
  type ColorValue,
  type ColorVisibility
} from './basicDataStore';

// API状态选择器
export const useApiSyncStatus = () =>
  useApiEnhancedBasicDataStore((state) => state.apiSyncState);